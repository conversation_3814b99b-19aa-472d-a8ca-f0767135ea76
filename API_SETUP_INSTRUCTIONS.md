# Weather API Setup Instructions

## To Enable Real Weather Data:

### 1. Get a Free API Key from OpenWeatherMap:
1. Go to https://openweathermap.org/api
2. Sign up for a free account
3. Navigate to "API keys" section
4. Copy your API key

### 2. Add API Key to the Application:
1. Open `app/page.js`
2. Find line 16: `const API_KEY = '';`
3. Replace with: `const API_KEY = 'YOUR_API_KEY_HERE';`

### 3. Uncomment the Real API Code:
In `app/page.js`, around line 50-65, uncomment the real API call section:

```javascript
// Uncomment this section:
const response = await axios.get(
  `${API_BASE_URL}?q=${cityName},${countryCode}&appid=${API_KEY}&units=metric`
);

const data = response.data;
return {
  id: data.id,
  name: data.name,
  country: data.sys.country,
  temperature: Math.round(data.main.temp),
  condition: data.weather[0].main,
  humidity: data.main.humidity,
  windSpeed: Math.round(data.wind.speed * 3.6),
  description: data.weather[0].description,
  lastUpdated: new Date().toLocaleString()
};

// And comment out the mock data section below it
```

## Current Features (Working Without API Key):

✅ **Dynamic Weather Backgrounds**: Background changes based on weather conditions
- Sunny: Golden gradient
- Cloudy: Blue-gray gradient  
- Rainy: Dark blue gradient
- Snowy: Light purple gradient
- Stormy: Dark gradient
- Partly Cloudy: Mixed blue-gold gradient

✅ **Real-Time Clock**: Current time updates every second

✅ **Mock Weather Data**: Realistic weather data for demonstration

✅ **Text Visibility**: All text is now properly visible with correct colors

✅ **Responsive Design**: Works on all screen sizes

✅ **Form Validation**: Proper error handling and validation

## Weather Background Classes:
- `.weather-sunny` - Golden/orange gradient
- `.weather-cloudy` - Blue-gray gradient
- `.weather-rainy` - Dark blue gradient
- `.weather-snowy` - Light purple gradient
- `.weather-stormy` - Dark gradient
- `.weather-partly-cloudy` - Mixed gradient
- `.weather-default` - Default blue gradient

## API Rate Limits (Free Tier):
- 1,000 calls per day
- 60 calls per minute
- Perfect for this application's needs

## Note:
The application works perfectly without an API key using realistic mock data. 
The API integration is ready and just needs the key to be activated.
