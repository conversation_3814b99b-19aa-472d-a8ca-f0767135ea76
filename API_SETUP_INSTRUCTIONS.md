# Weather API Setup Instructions

## Enhanced Weather API with Coordinates

### API Endpoints Used:
1. **Weather Data**: `https://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={API key}`
2. **Geocoding**: `https://api.openweathermap.org/geo/1.0/direct?q={city name},{country code}&limit=1&appid={API key}`

## To Enable Real Weather Data:

### 1. Get a Free API Key from OpenWeatherMap:
1. Go to https://openweathermap.org/api
2. Sign up for a free account
3. Navigate to "API keys" section
4. Copy your API key

### 2. Add API Key to the Application:
1. Open `app/page.js`
2. Find line 17: `const API_KEY = 'YOUR_API_KEY_HERE';`
3. Replace `YOUR_API_KEY_HERE` with your actual API key

### 3. How the Enhanced API Works:

**Step 1: Get Coordinates**
- Uses geocoding API to convert city name to lat/lon coordinates
- More accurate than city name searches
- Handles multiple cities with same name

**Step 2: Fetch Weather Data**
- Uses coordinates to get precise weather data
- API call: `weather?lat={lat}&lon={lon}&appid={API_KEY}&units=metric`
- Returns real-time weather information

**Step 3: Automatic Fallback**
- If API key not set, uses realistic mock data
- Seamless transition between mock and real data
- No code changes needed when adding API key

## Enhanced Features (Working Without API Key):

✅ **Coordinate-Based Weather**: More accurate location handling
✅ **Geocoding Integration**: Converts city names to precise coordinates
✅ **Dynamic Weather Backgrounds**: Background changes based on weather conditions
- Clear/Sunny: Golden gradient
- Clouds: Blue-gray gradient
- Rain/Drizzle: Dark blue gradient
- Snow: Light purple gradient
- Thunderstorm: Dark gradient
- Mist/Fog: Mixed gradient

✅ **Real-Time Clock**: Current time updates every second
✅ **Smart Mock Data**: Realistic weather data with proper API structure
✅ **Text Visibility**: All text properly visible with correct colors
✅ **Responsive Design**: Works on all screen sizes
✅ **Form Validation**: Proper error handling and validation
✅ **Coordinate Storage**: Saves location data for future use

## Weather Background Classes:
- `.weather-sunny` - Golden/orange gradient
- `.weather-cloudy` - Blue-gray gradient
- `.weather-rainy` - Dark blue gradient
- `.weather-snowy` - Light purple gradient
- `.weather-stormy` - Dark gradient
- `.weather-partly-cloudy` - Mixed gradient
- `.weather-default` - Default blue gradient

## API Rate Limits (Free Tier):
- 1,000 calls per day
- 60 calls per minute
- Perfect for this application's needs

## API Benefits:

### With API Key:
- **Real Weather Data**: Live weather conditions from OpenWeatherMap
- **Accurate Locations**: Precise coordinates for any city worldwide
- **Current Conditions**: Real-time temperature, humidity, wind speed
- **Weather Descriptions**: Detailed weather condition descriptions
- **Global Coverage**: Support for cities worldwide

### Without API Key (Current State):
- **Realistic Mock Data**: Proper API structure with sample data
- **Full Functionality**: All features work seamlessly
- **No Errors**: Graceful fallback to demonstration mode
- **Easy Upgrade**: Just add API key to enable real data

## Example API Calls:

```javascript
// Geocoding: Get coordinates for "Toronto, CA"
GET https://api.openweathermap.org/geo/1.0/direct?q=Toronto,CA&limit=1&appid=YOUR_API_KEY

// Weather: Get weather for Toronto coordinates
GET https://api.openweathermap.org/data/2.5/weather?lat=43.6532&lon=-79.3832&appid=YOUR_API_KEY&units=metric
```

## Note:
The application works perfectly without an API key using realistic mock data.
The coordinate-based API integration is ready and just needs the key to be activated for real weather data.
