/* City Search Component Styles */
.container {
  margin-bottom: 20px;
}

.searchCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(116, 185, 255, 0.3);
  max-width: 600px;
  margin: 0 auto;
}

.title {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border-bottom: 2px solid #74b9ff;
  padding-bottom: 10px;
}

.searchForm {
  position: relative;
}

.searchInputContainer {
  position: relative;
  margin-bottom: 15px;
}

.searchInput {
  width: 100%;
  padding: 15px 50px 15px 20px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.searchInput:focus {
  outline: none;
  border-color: #74b9ff;
  box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
  transform: translateY(-1px);
}

.searchInput::placeholder {
  color: #999;
  font-style: italic;
}

.searchInput:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

.searchIcon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  color: #74b9ff;
  pointer-events: none;
}

.suggestionsContainer {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #74b9ff;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 5px;
}

.suggestionsHeader {
  padding: 12px 20px;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  border-radius: 13px 13px 0 0;
}

.suggestionsList {
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestionItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f3f4;
}

.suggestionItem:hover,
.suggestionItem.selected {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.suggestionItem:last-child {
  border-bottom: none;
  border-radius: 0 0 13px 13px;
}

.cityName {
  font-weight: 600;
  font-size: 1rem;
}

.countryName {
  font-size: 0.85rem;
  opacity: 0.8;
  background: rgba(116, 185, 255, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.suggestionItem.selected .countryName,
.suggestionItem:hover .countryName {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.noResults {
  padding: 20px;
  text-align: center;
  color: #666;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  margin-top: 5px;
}

.noResults p {
  margin: 5px 0;
}

.noResults p:first-child {
  font-weight: 600;
  color: #333;
}

.buttonGroup {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.searchButton,
.cancelButton {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.searchButton {
  background: #00b894;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.searchButton:hover:not(:disabled) {
  background: #00a085;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
}

.searchButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancelButton {
  background: #6c757d;
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.cancelButton:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.quickSelect {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.quickSelectTitle {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.popularCities {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
}

.popularCityButton {
  padding: 8px 12px;
  background: rgba(116, 185, 255, 0.1);
  border: 2px solid rgba(116, 185, 255, 0.3);
  border-radius: 20px;
  color: #0984e3;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.popularCityButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}

.popularCityButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.searchTips {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.tipsTitle {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.tipsList {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.tipsList li {
  margin-bottom: 5px;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .searchCard {
    padding: 20px;
    margin: 0 10px;
  }
  
  .title {
    font-size: 1.3rem;
  }
  
  .buttonGroup {
    flex-direction: column;
    gap: 10px;
  }
  
  .searchButton,
  .cancelButton {
    width: 100%;
  }
  
  .popularCities {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .searchCard {
    padding: 15px;
    margin: 0 5px;
  }
  
  .title {
    font-size: 1.2rem;
  }
  
  .searchInput {
    padding: 12px 40px 12px 15px;
    font-size: 0.9rem;
  }
  
  .searchButton,
  .cancelButton {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

/* Animation for component appearance */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.searchCard {
  animation: slideIn 0.3s ease-out;
}
