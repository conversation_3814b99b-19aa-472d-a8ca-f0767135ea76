'use client';

import styles from './WeatherList.module.css';

export default function WeatherList({ cities, onCitySelect, onRemoveCity, selectedCity }) {
  if (cities.length === 0) {
    return (
      <div className={styles.container}>
        <h2 className={styles.title}>Weather Cities</h2>
        <div className={styles.emptyMessage}>
          <p>No cities to display. Add a city to get started!</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <h2 className={styles.title}>Weather Cities ({cities.length})</h2>
      <div className={styles.cityList}>
        {cities.map((city) => (
          <div 
            key={city.id}
            className={`${styles.cityCard} ${
              selectedCity && selectedCity.id === city.id ? styles.selected : ''
            }`}
            onClick={() => onCitySelect(city)}
          >
            <div className={styles.cityInfo}>
              <h3 className={styles.cityName}>{city.name}</h3>
              <p className={styles.country}>{city.country}</p>
              <div className={styles.weatherSummary}>
                <span className={styles.temperature}>{city.temperature}°C</span>
                <span className={styles.condition}>{city.condition}</span>
              </div>
            </div>
            <div className={styles.actions}>
              <button 
                className={styles.removeButton}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent city selection when removing
                  onRemoveCity(city.id);
                }}
                title="Remove city"
              >
                ✕
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
