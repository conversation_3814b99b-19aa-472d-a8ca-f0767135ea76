'use client';

import { useState, useEffect } from 'react';
import WeatherList from './components/WeatherList';
import WeatherDetails from './components/WeatherDetails';
import AddCityForm from './components/AddCityForm';
import styles from './page.module.css';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  // Sample weather data for demonstration
  const [cities, setCities] = useState([
    {
      id: 1,
      name: 'Toronto',
      country: 'Canada',
      temperature: 22,
      condition: 'Sunny',
      humidity: 65,
      windSpeed: 15,
      description: 'Clear skies with gentle breeze'
    },
    {
      id: 2,
      name: 'Vancouver',
      country: 'Canada',
      temperature: 18,
      condition: 'Cloudy',
      humidity: 78,
      windSpeed: 12,
      description: 'Overcast with light clouds'
    },
    {
      id: 3,
      name: 'Montreal',
      country: 'Canada',
      temperature: 25,
      condition: 'Partly Cloudy',
      humidity: 60,
      windSpeed: 18,
      description: 'Mix of sun and clouds'
    }
  ]);

  const [selectedCity, setSelectedCity] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle city selection
  const handleCitySelect = (city) => {
    setSelectedCity(city);
    setError('');
  };

  // Handle adding new city
  const handleAddCity = (newCityData) => {
    // Check if city already exists
    const cityExists = cities.some(city =>
      city.name.toLowerCase() === newCityData.name.toLowerCase()
    );

    if (cityExists) {
      setError('City already exists in the list!');
      return;
    }

    // Generate consistent random values based on city name for demo purposes
    const cityNameHash = newCityData.name.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    const newCity = {
      id: cities.length + 1,
      ...newCityData,
      temperature: Math.abs(cityNameHash % 30) + 5, // Consistent temp between 5-35
      humidity: Math.abs(cityNameHash % 40) + 40, // Consistent humidity 40-80
      windSpeed: Math.abs(cityNameHash % 20) + 5, // Consistent wind 5-25
    };

    setCities([...cities, newCity]);
    setShowForm(false);
    setError('');
  };

  // Handle removing a city
  const handleRemoveCity = (cityId) => {
    setCities(cities.filter(city => city.id !== cityId));
    if (selectedCity && selectedCity.id === cityId) {
      setSelectedCity(null);
    }
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <h1 className={styles.title}>🌤️ Weather Watch</h1>
        <p className={styles.subtitle}>Your personal weather dashboard</p>
      </header>

      <main className={styles.main}>
        <div className={styles.controls}>
          <button
            className={styles.addButton}
            onClick={() => setShowForm(!showForm)}
          >
            {showForm ? 'Cancel' : 'Add City'}
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {showForm && (
          <AddCityForm
            onAddCity={handleAddCity}
            onCancel={() => setShowForm(false)}
          />
        )}

        <div className={styles.content}>
          <div className={styles.listSection}>
            <WeatherList
              cities={cities}
              onCitySelect={handleCitySelect}
              onRemoveCity={handleRemoveCity}
              selectedCity={selectedCity}
            />
          </div>

          <div className={styles.detailsSection}>
            {selectedCity ? (
              <WeatherDetails city={selectedCity} />
            ) : (
              <div className={styles.placeholder}>
                <h3>Select a city to view weather details</h3>
                <p>Click on any city from the list to see detailed weather information.</p>
              </div>
            )}
          </div>
        </div>

        {cities.length === 0 && (
          <div className={styles.emptyState}>
            <h3>No cities added yet</h3>
            <p>Add your first city to start tracking weather!</p>
          </div>
        )}
      </main>
    </div>
  );
}
