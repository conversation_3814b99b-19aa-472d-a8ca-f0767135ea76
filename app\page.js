'use client';

import { useState } from 'react';
import MainNavigation from './components/MainNavigation';
import WeatherWatch from './components/WeatherWatch';
import FitnessTracker from './components/FitnessTracker';
import ClientOnly from './components/ClientOnly';

export default function Home() {
  const [activeApp, setActiveApp] = useState('weather');

  const handleAppChange = (appId) => {
    setActiveApp(appId);
  };

  return (
    <div>
      <MainNavigation
        activeApp={activeApp}
        onAppChange={handleAppChange}
      />

      <ClientOnly fallback={
        <div style={{
          minHeight: '80vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        }}>
          <div style={{ textAlign: 'center' }}>
            <h2>Loading Life Dashboard...</h2>
            <p>Please wait while we prepare your applications.</p>
          </div>
        </div>
      }>
        {activeApp === 'weather' && <WeatherWatch />}
        {activeApp === 'fitness' && <FitnessTracker />}
      </ClientOnly>
    </div>
  );
}
