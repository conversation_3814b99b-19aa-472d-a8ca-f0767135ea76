'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';
import WeatherList from './components/WeatherList';
import WeatherDetails from './components/WeatherDetails';
import AddCityForm from './components/AddCityForm';
import styles from './page.module.css';

export default function Home() {
  const [mounted, setMounted] = useState(false);
  const [cities, setCities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState('');

  // Weather API configuration (using OpenWeatherMap free tier)
  const API_KEY = ''; // You'll need to get a free API key from openweathermap.org
  const API_BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';

  // Default cities to load
  const defaultCities = [
    { name: 'Toronto', country: 'CA' },
    { name: 'Vancouver', country: 'CA' },
    { name: 'Montreal', country: 'CA' },
    { name: 'New York', country: 'US' },
    { name: 'London', country: 'GB' }
  ];

  const [selectedCity, setSelectedCity] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [error, setError] = useState('');

  // Get weather background class based on condition
  const getWeatherBackgroundClass = (condition) => {
    if (!condition) return 'weather-default';

    const conditionLower = condition.toLowerCase();
    if (conditionLower.includes('sun') || conditionLower.includes('clear')) {
      return 'weather-sunny';
    } else if (conditionLower.includes('cloud')) {
      return conditionLower.includes('partly') ? 'weather-partly-cloudy' : 'weather-cloudy';
    } else if (conditionLower.includes('rain') || conditionLower.includes('drizzle')) {
      return 'weather-rainy';
    } else if (conditionLower.includes('snow')) {
      return 'weather-snowy';
    } else if (conditionLower.includes('storm') || conditionLower.includes('thunder')) {
      return 'weather-stormy';
    }
    return 'weather-default';
  };

  // Fetch weather data from API
  const fetchWeatherData = async (cityName, countryCode) => {
    try {
      // For demo purposes, we'll use mock data since API key is needed
      // In production, uncomment the API call below

      /*
      const response = await axios.get(
        `${API_BASE_URL}?q=${cityName},${countryCode}&appid=${API_KEY}&units=metric`
      );

      const data = response.data;
      return {
        id: data.id,
        name: data.name,
        country: data.sys.country,
        temperature: Math.round(data.main.temp),
        condition: data.weather[0].main,
        humidity: data.main.humidity,
        windSpeed: Math.round(data.wind.speed * 3.6), // Convert m/s to km/h
        description: data.weather[0].description,
        lastUpdated: new Date().toLocaleString()
      };
      */

      // Mock data for demonstration
      const mockConditions = ['Sunny', 'Cloudy', 'Partly Cloudy', 'Rainy', 'Clear'];
      const randomCondition = mockConditions[Math.floor(Math.random() * mockConditions.length)];

      return {
        id: Date.now() + Math.random(),
        name: cityName,
        country: countryCode,
        temperature: Math.floor(Math.random() * 30) + 5,
        condition: randomCondition,
        humidity: Math.floor(Math.random() * 40) + 40,
        windSpeed: Math.floor(Math.random() * 20) + 5,
        description: `Current weather in ${cityName}`,
        lastUpdated: new Date().toLocaleString()
      };
    } catch (error) {
      console.error('Error fetching weather data:', error);
      return null;
    }
  };

  // Load initial cities
  useEffect(() => {
    const loadInitialCities = async () => {
      setLoading(true);
      const cityPromises = defaultCities.map(city =>
        fetchWeatherData(city.name, city.country)
      );

      const cityData = await Promise.all(cityPromises);
      const validCities = cityData.filter(city => city !== null);

      setCities(validCities);
      setLoading(false);
    };

    loadInitialCities();
  }, []);

  // Update current time
  useEffect(() => {
    const updateTime = () => {
      setCurrentTime(new Date().toLocaleString());
    };

    updateTime();
    const timeInterval = setInterval(updateTime, 1000);

    return () => clearInterval(timeInterval);
  }, []);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle city selection
  const handleCitySelect = (city) => {
    setSelectedCity(city);
    setError('');
  };

  // Handle adding new city
  const handleAddCity = async (newCityData) => {
    // Check if city already exists
    const cityExists = cities.some(city =>
      city.name.toLowerCase() === newCityData.name.toLowerCase()
    );

    if (cityExists) {
      setError('City already exists in the list!');
      return;
    }

    setLoading(true);

    // Fetch real weather data for the new city
    const weatherData = await fetchWeatherData(newCityData.name, newCityData.country);

    if (weatherData) {
      setCities([...cities, weatherData]);
      setShowForm(false);
      setError('');
    } else {
      setError('Could not fetch weather data for this city. Please try again.');
    }

    setLoading(false);
  };

  // Handle removing a city
  const handleRemoveCity = (cityId) => {
    setCities(cities.filter(city => city.id !== cityId));
    if (selectedCity && selectedCity.id === cityId) {
      setSelectedCity(null);
    }
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  // Get background class based on selected city or first city
  const backgroundWeather = selectedCity ? selectedCity.condition : (cities.length > 0 ? cities[0].condition : 'default');
  const backgroundClass = getWeatherBackgroundClass(backgroundWeather);

  return (
    <div className={`${styles.container} ${backgroundClass}`}>
      <header className={styles.header}>
        <h1 className={styles.title}>🌤️ Weather Watch</h1>
        <p className={styles.subtitle}>Your personal weather dashboard</p>
        <p className={styles.currentTime}>Current Time: {currentTime}</p>
      </header>

      <main className={styles.main}>
        <div className={styles.controls}>
          <button
            className={styles.addButton}
            onClick={() => setShowForm(!showForm)}
          >
            {showForm ? 'Cancel' : 'Add City'}
          </button>
        </div>

        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {showForm && (
          <AddCityForm
            onAddCity={handleAddCity}
            onCancel={() => setShowForm(false)}
          />
        )}

        <div className={styles.content}>
          <div className={styles.listSection}>
            <WeatherList
              cities={cities}
              onCitySelect={handleCitySelect}
              onRemoveCity={handleRemoveCity}
              selectedCity={selectedCity}
            />
          </div>

          <div className={styles.detailsSection}>
            {selectedCity ? (
              <WeatherDetails city={selectedCity} />
            ) : (
              <div className={styles.placeholder}>
                <h3>Select a city to view weather details</h3>
                <p>Click on any city from the list to see detailed weather information.</p>
              </div>
            )}
          </div>
        </div>

        {loading && (
          <div className={styles.loading}>
            <h3>Loading weather data...</h3>
            <p>Please wait while we fetch the latest weather information.</p>
          </div>
        )}

        {!loading && cities.length === 0 && (
          <div className={styles.emptyState}>
            <h3>No cities added yet</h3>
            <p>Add your first city to start tracking weather!</p>
          </div>
        )}
      </main>
    </div>
  );
}
