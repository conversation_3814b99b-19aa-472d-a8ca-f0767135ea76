/* Weather Watch Main Page Styles */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: background 0.5s ease;
  color: #ffffff;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.title {
  font-size: 3rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 700;
}

.subtitle {
  font-size: 1.2rem;
  margin: 10px 0 0 0;
  opacity: 0.9;
  font-weight: 300;
  color: #ffffff;
}

.currentTime {
  font-size: 1rem;
  margin: 10px 0 0 0;
  opacity: 0.8;
  font-weight: 400;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.main {
  max-width: 1200px;
  margin: 0 auto;
}

.controls {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.addButton {
  background: #00b894;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.addButton:hover {
  background: #00a085;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
}

.error {
  background: #ff6b6b;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  margin: 10px auto;
  max-width: 500px;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-top: 20px;
}

.listSection {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.detailsSection {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.placeholder {
  text-align: center;
  color: #666;
  padding: 40px 20px;
}

.placeholder h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.5rem;
}

.placeholder p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.8;
}

.emptyState {
  text-align: center;
  color: white;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  margin-top: 30px;
  backdrop-filter: blur(10px);
}

.emptyState h3 {
  margin: 0 0 10px 0;
  font-size: 1.8rem;
}

.emptyState p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.loading {
  text-align: center;
  color: white;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  margin-top: 30px;
  backdrop-filter: blur(10px);
}

.loading h3 {
  margin: 0 0 10px 0;
  font-size: 1.8rem;
  color: #ffffff;
}

.loading p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .title {
    font-size: 2.5rem;
  }

  .content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .listSection,
  .detailsSection {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .addButton {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}
