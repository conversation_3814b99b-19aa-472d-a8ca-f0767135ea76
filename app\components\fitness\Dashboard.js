'use client';

export default function FitnessDashboard({ workouts, goals, progress }) {
  // Calculate statistics
  const totalWorkouts = workouts.length;
  const totalCalories = workouts.reduce((sum, workout) => sum + workout.calories, 0);
  const totalDuration = workouts.reduce((sum, workout) => sum + workout.duration, 0);
  const completedGoals = goals.filter(goal => goal.current >= goal.target).length;
  
  const recentWorkouts = workouts.slice(0, 3);
  const activeGoals = goals.slice(0, 3);

  const StatCard = ({ title, value, unit, icon, color }) => (
    <div style={{ 
      background: 'white',
      borderRadius: '12px',
      padding: '24px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb',
      textAlign: 'center',
      background: `linear-gradient(135deg, ${color}20, ${color}10)`,
      border: `2px solid ${color}30`
    }}>
      <div style={{ fontSize: '2rem', marginBottom: '10px' }}>{icon}</div>
      <h3 style={{ margin: '0 0 5px 0', color: '#333', fontSize: '2rem', fontWeight: '700' }}>
        {value}
        <span style={{ fontSize: '1rem', color: '#666', fontWeight: '400' }}> {unit}</span>
      </h3>
      <p style={{ margin: 0, color: '#666', fontWeight: '500' }}>{title}</p>
    </div>
  );

  const WorkoutCard = ({ workout }) => (
    <div style={{ 
      background: 'white',
      borderRadius: '12px',
      padding: '20px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb',
      marginBottom: '15px',
      borderLeft: '4px solid #4f46e5'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <h4 style={{ margin: 0, color: '#333' }}>{workout.name}</h4>
        <span style={{ 
          padding: '4px 12px',
          borderRadius: '20px',
          fontSize: '12px',
          fontWeight: '600',
          textTransform: 'uppercase',
          background: '#dbeafe',
          color: '#1e40af'
        }}>{workout.type}</span>
      </div>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))', gap: '10px', fontSize: '14px', color: '#666' }}>
        <div>⏱️ {workout.duration} min</div>
        <div>🔥 {workout.calories} cal</div>
        <div>📅 {workout.date}</div>
      </div>
      {workout.notes && (
        <p style={{ margin: '10px 0 0 0', fontSize: '14px', color: '#666', fontStyle: 'italic' }}>
          "{workout.notes}"
        </p>
      )}
    </div>
  );

  const GoalCard = ({ goal }) => {
    const progress = Math.min((goal.current / goal.target) * 100, 100);
    const isCompleted = goal.current >= goal.target;
    
    return (
      <div style={{ 
        background: 'white',
        borderRadius: '12px',
        padding: '20px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        marginBottom: '15px',
        borderLeft: `4px solid ${isCompleted ? '#10b981' : '#f59e0b'}`
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
          <h4 style={{ margin: 0, color: '#333' }}>{goal.title}</h4>
          <span style={{ 
            padding: '4px 12px',
            borderRadius: '20px',
            fontSize: '12px',
            fontWeight: '600',
            textTransform: 'uppercase',
            background: isCompleted ? '#dcfce7' : '#fef3c7',
            color: isCompleted ? '#166534' : '#92400e'
          }}>
            {isCompleted ? 'Completed' : 'In Progress'}
          </span>
        </div>
        <div style={{ marginBottom: '10px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '14px', color: '#666', marginBottom: '5px' }}>
            <span>{goal.current} / {goal.target} {goal.unit}</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div style={{ 
            width: '100%',
            height: '8px',
            background: '#e5e7eb',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            <div style={{ 
              height: '100%',
              background: isCompleted ? '#10b981' : '#f59e0b',
              width: `${progress}%`,
              transition: 'width 0.3s ease'
            }}></div>
          </div>
        </div>
        <div style={{ fontSize: '14px', color: '#666' }}>
          📅 Deadline: {goal.deadline}
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* Statistics Overview */}
      <div style={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '24px',
        marginBottom: '40px'
      }}>
        <StatCard 
          title="Total Workouts" 
          value={totalWorkouts} 
          unit="sessions" 
          icon="💪" 
          color="#4f46e5"
        />
        <StatCard 
          title="Calories Burned" 
          value={totalCalories} 
          unit="cal" 
          icon="🔥" 
          color="#ef4444"
        />
        <StatCard 
          title="Total Duration" 
          value={totalDuration} 
          unit="min" 
          icon="⏱️" 
          color="#06b6d4"
        />
        <StatCard 
          title="Goals Completed" 
          value={completedGoals} 
          unit={`/ ${goals.length}`} 
          icon="🎯" 
          color="#10b981"
        />
      </div>

      {/* Recent Activity */}
      <div style={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px'
      }}>
        {/* Recent Workouts */}
        <div>
          <h2 style={{ margin: '0 0 20px 0', color: '#333', display: 'flex', alignItems: 'center', gap: '10px' }}>
            <span>💪</span> Recent Workouts
          </h2>
          {recentWorkouts.length > 0 ? (
            recentWorkouts.map(workout => (
              <WorkoutCard key={workout.id} workout={workout} />
            ))
          ) : (
            <div style={{ 
              background: 'white',
              borderRadius: '12px',
              padding: '40px 20px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
              border: '1px solid #e5e7eb',
              textAlign: 'center',
              color: '#666'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🏃‍♂️</div>
              <h3>No workouts yet</h3>
              <p>Start tracking your fitness journey!</p>
            </div>
          )}
        </div>

        {/* Active Goals */}
        <div>
          <h2 style={{ margin: '0 0 20px 0', color: '#333', display: 'flex', alignItems: 'center', gap: '10px' }}>
            <span>🎯</span> Active Goals
          </h2>
          {activeGoals.length > 0 ? (
            activeGoals.map(goal => (
              <GoalCard key={goal.id} goal={goal} />
            ))
          ) : (
            <div style={{ 
              background: 'white',
              borderRadius: '12px',
              padding: '40px 20px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
              border: '1px solid #e5e7eb',
              textAlign: 'center',
              color: '#666'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🎯</div>
              <h3>No goals set</h3>
              <p>Set your first fitness goal!</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
