'use client';

import { useState } from 'react';

export default function WorkoutTracker({ workouts, onAddWorkout, onDeleteWorkout }) {
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    type: 'Cardio',
    duration: '',
    calories: '',
    notes: ''
  });

  const workoutTypes = [
    'Cardio',
    'Strength',
    'Flexibility',
    'Sports',
    'Yoga',
    'Pilates',
    'Swimming',
    'Cycling',
    'Running',
    'Walking'
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.name && formData.duration && formData.calories) {
      onAddWorkout({
        ...formData,
        duration: parseInt(formData.duration),
        calories: parseInt(formData.calories)
      });
      setFormData({
        name: '',
        type: 'Cardio',
        duration: '',
        calories: '',
        notes: ''
      });
      setShowForm(false);
    }
  };

  const getWorkoutIcon = (type) => {
    const icons = {
      'Cardio': '🏃‍♂️',
      'Strength': '🏋️‍♂️',
      'Flexibility': '🤸‍♂️',
      'Sports': '⚽',
      'Yoga': '🧘‍♂️',
      'Pilates': '🤸‍♀️',
      'Swimming': '🏊‍♂️',
      'Cycling': '🚴‍♂️',
      'Running': '🏃‍♂️',
      'Walking': '🚶‍♂️'
    };
    return icons[type] || '💪';
  };

  const buttonStyle = {
    padding: '12px 24px',
    border: 'none',
    borderRadius: '8px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    display: 'inline-flex',
    alignItems: 'center',
    gap: '8px',
    textDecoration: 'none',
    fontSize: '14px'
  };

  const primaryButtonStyle = {
    ...buttonStyle,
    background: '#4f46e5',
    color: 'white'
  };

  const dangerButtonStyle = {
    ...buttonStyle,
    background: '#ef4444',
    color: 'white'
  };

  const successButtonStyle = {
    ...buttonStyle,
    background: '#10b981',
    color: 'white'
  };

  const cardStyle = {
    background: 'white',
    borderRadius: '12px',
    padding: '24px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e5e7eb'
  };

  const inputStyle = {
    width: '100%',
    padding: '12px 16px',
    border: '2px solid #e5e7eb',
    borderRadius: '8px',
    fontSize: '16px',
    transition: 'border-color 0.3s ease'
  };

  const WorkoutCard = ({ workout }) => (
    <div style={{ 
      ...cardStyle,
      marginBottom: '20px',
      borderLeft: '4px solid #4f46e5'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '15px' }}>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
            <span style={{ fontSize: '1.5rem' }}>{getWorkoutIcon(workout.type)}</span>
            <h3 style={{ margin: 0, color: '#333' }}>{workout.name}</h3>
            <span style={{ 
              padding: '4px 12px',
              borderRadius: '20px',
              fontSize: '12px',
              fontWeight: '600',
              textTransform: 'uppercase',
              background: '#dbeafe',
              color: '#1e40af'
            }}>{workout.type}</span>
          </div>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '15px', marginBottom: '10px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px', color: '#666' }}>
              <span>⏱️</span>
              <span>{workout.duration} minutes</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px', color: '#666' }}>
              <span>🔥</span>
              <span>{workout.calories} calories</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '5px', color: '#666' }}>
              <span>📅</span>
              <span>{workout.date}</span>
            </div>
          </div>
          
          {workout.notes && (
            <p style={{ margin: 0, fontSize: '14px', color: '#666', fontStyle: 'italic', padding: '10px', background: '#f8fafc', borderRadius: '6px' }}>
              💭 {workout.notes}
            </p>
          )}
        </div>
        
        <button 
          onClick={() => onDeleteWorkout(workout.id)}
          style={{ ...dangerButtonStyle, padding: '8px 12px', fontSize: '12px' }}
        >
          🗑️ Delete
        </button>
      </div>
    </div>
  );

  return (
    <div>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '30px' }}>
        <h2 style={{ margin: 0, color: '#333', display: 'flex', alignItems: 'center', gap: '10px' }}>
          <span>💪</span> Workout Tracker
        </h2>
        <button 
          onClick={() => setShowForm(!showForm)}
          style={primaryButtonStyle}
        >
          <span>{showForm ? '❌' : '➕'}</span>
          {showForm ? 'Cancel' : 'Add Workout'}
        </button>
      </div>

      {/* Add Workout Form */}
      {showForm && (
        <div style={{ 
          ...cardStyle,
          marginBottom: '30px',
          background: 'linear-gradient(135deg, #4f46e520, #4f46e510)',
          border: '2px solid #4f46e530'
        }}>
          <h3 style={{ margin: '0 0 20px 0', color: '#333', display: 'flex', alignItems: 'center', gap: '10px' }}>
            <span>📝</span> Log New Workout
          </h3>
          
          <form onSubmit={handleSubmit}>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '24px', marginBottom: '20px' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333' }}>
                  Workout Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  style={inputStyle}
                  placeholder="e.g., Morning Run, Chest Day"
                  required
                />
              </div>
              
              <div>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333' }}>
                  Workout Type
                </label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  style={inputStyle}
                >
                  {workoutTypes.map(type => (
                    <option key={type} value={type}>{getWorkoutIcon(type)} {type}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '24px', marginBottom: '20px' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333' }}>
                  Duration (minutes) *
                </label>
                <input
                  type="number"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  style={inputStyle}
                  placeholder="30"
                  min="1"
                  required
                />
              </div>
              
              <div>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333' }}>
                  Calories Burned *
                </label>
                <input
                  type="number"
                  name="calories"
                  value={formData.calories}
                  onChange={handleInputChange}
                  style={inputStyle}
                  placeholder="300"
                  min="1"
                  required
                />
              </div>
            </div>
            
            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333' }}>
                Notes (optional)
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                style={{ ...inputStyle, resize: 'vertical', minHeight: '100px' }}
                placeholder="How did the workout feel? Any observations..."
                rows="3"
              />
            </div>
            
            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
              <button 
                type="button" 
                onClick={() => setShowForm(false)}
                style={{ ...buttonStyle, background: '#6b7280', color: 'white' }}
              >
                Cancel
              </button>
              <button type="submit" style={successButtonStyle}>
                <span>💾</span> Save Workout
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Workouts List */}
      <div>
        {workouts.length > 0 ? (
          <>
            <div style={{ marginBottom: '20px', color: '#666', fontSize: '14px' }}>
              Showing {workouts.length} workout{workouts.length !== 1 ? 's' : ''}
            </div>
            {workouts.map(workout => (
              <WorkoutCard key={workout.id} workout={workout} />
            ))}
          </>
        ) : (
          <div style={{ ...cardStyle, textAlign: 'center', padding: '60px 20px', color: '#666' }}>
            <div style={{ fontSize: '4rem', marginBottom: '20px' }}>🏃‍♂️</div>
            <h3 style={{ margin: '0 0 10px 0' }}>No workouts logged yet</h3>
            <p style={{ margin: '0 0 20px 0' }}>Start your fitness journey by logging your first workout!</p>
            <button 
              onClick={() => setShowForm(true)}
              style={primaryButtonStyle}
            >
              <span>➕</span> Add Your First Workout
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
