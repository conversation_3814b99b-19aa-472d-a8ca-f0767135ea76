'use client';

import { useState } from 'react';
import styles from './AddCityForm.module.css';

export default function AddCityForm({ onAddCity, onCancel }) {
  const [formData, setFormData] = useState({
    name: '',
    country: '',
    condition: '<PERSON>',
    description: ''
  });
  const [errors, setErrors] = useState({});

  const weatherConditions = [
    'Sunny',
    'Cloudy',
    'Partly Cloudy',
    'Rainy',
    'Snowy',
    'Stormy'
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'City name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'City name must be at least 2 characters';
    }

    if (!formData.country.trim()) {
      newErrors.country = 'Country is required';
    } else if (formData.country.trim().length < 2) {
      newErrors.country = 'Country must be at least 2 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Weather description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      onAddCity({
        name: formData.name.trim(),
        country: formData.country.trim(),
        condition: formData.condition,
        description: formData.description.trim()
      });
      
      // Reset form
      setFormData({
        name: '',
        country: '',
        condition: 'Sunny',
        description: ''
      });
      setErrors({});
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.formCard}>
        <h3 className={styles.title}>Add New City</h3>
        
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="name" className={styles.label}>
              City Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`${styles.input} ${errors.name ? styles.inputError : ''}`}
              placeholder="Enter city name"
            />
            {errors.name && (
              <span className={styles.errorMessage}>{errors.name}</span>
            )}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="country" className={styles.label}>
              Country *
            </label>
            <input
              type="text"
              id="country"
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              className={`${styles.input} ${errors.country ? styles.inputError : ''}`}
              placeholder="Enter country name"
            />
            {errors.country && (
              <span className={styles.errorMessage}>{errors.country}</span>
            )}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="condition" className={styles.label}>
              Weather Condition
            </label>
            <select
              id="condition"
              name="condition"
              value={formData.condition}
              onChange={handleInputChange}
              className={styles.select}
            >
              {weatherConditions.map(condition => (
                <option key={condition} value={condition}>
                  {condition}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description" className={styles.label}>
              Weather Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              className={`${styles.textarea} ${errors.description ? styles.inputError : ''}`}
              placeholder="Describe the current weather conditions"
              rows="3"
            />
            {errors.description && (
              <span className={styles.errorMessage}>{errors.description}</span>
            )}
          </div>

          <div className={styles.buttonGroup}>
            <button
              type="button"
              onClick={onCancel}
              className={styles.cancelButton}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={styles.submitButton}
            >
              Add City
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
