'use client';

import { useState, useEffect, useRef } from 'react';
import styles from './CitySearch.module.css';

export default function CitySearch({ onCitySelect, onCancel, isLoading }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchInputRef = useRef(null);

  // Popular cities for quick selection
  const popularCities = [
    { name: 'Toronto', country: 'CA', fullName: 'Toronto, Canada' },
    { name: 'Vancouver', country: 'CA', fullName: 'Vancouver, Canada' },
    { name: 'Montreal', country: 'CA', fullName: 'Montreal, Canada' },
    { name: 'New York', country: 'US', fullName: 'New York, United States' },
    { name: 'London', country: 'GB', fullName: 'London, United Kingdom' },
    { name: 'Paris', country: 'FR', fullName: 'Paris, France' },
    { name: 'Tokyo', country: 'JP', fullName: 'Tokyo, Japan' },
    { name: 'Sydney', country: 'AU', fullName: 'Sydney, Australia' },
    { name: 'Berlin', country: 'DE', fullName: 'Berlin, Germany' },
    { name: 'Rome', country: 'IT', fullName: 'Rome, Italy' },
    { name: 'Madrid', country: 'ES', fullName: 'Madrid, Spain' },
    { name: 'Amsterdam', country: 'NL', fullName: 'Amsterdam, Netherlands' },
    { name: 'Stockholm', country: 'SE', fullName: 'Stockholm, Sweden' },
    { name: 'Oslo', country: 'NO', fullName: 'Oslo, Norway' },
    { name: 'Copenhagen', country: 'DK', fullName: 'Copenhagen, Denmark' },
    { name: 'Helsinki', country: 'FI', fullName: 'Helsinki, Finland' },
    { name: 'Vienna', country: 'AT', fullName: 'Vienna, Austria' },
    { name: 'Zurich', country: 'CH', fullName: 'Zurich, Switzerland' },
    { name: 'Brussels', country: 'BE', fullName: 'Brussels, Belgium' },
    { name: 'Dublin', country: 'IE', fullName: 'Dublin, Ireland' }
  ];

  // Filter suggestions based on search term
  useEffect(() => {
    if (searchTerm.length >= 2) {
      const filtered = popularCities.filter(city =>
        city.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        city.fullName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSuggestions(filtered);
      setShowSuggestions(true);
      setSelectedIndex(-1);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchTerm]);

  // Focus input when component mounts
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  const handleInputChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCityClick = (city) => {
    onCitySelect(city);
    setSearchTerm('');
    setShowSuggestions(false);
  };

  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleCityClick(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
      handleCityClick(suggestions[selectedIndex]);
    } else if (suggestions.length > 0) {
      handleCityClick(suggestions[0]);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.searchCard}>
        <h3 className={styles.title}>🔍 Search Cities</h3>
        
        <form onSubmit={handleSubmit} className={styles.searchForm}>
          <div className={styles.searchInputContainer}>
            <input
              ref={searchInputRef}
              type="text"
              value={searchTerm}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className={styles.searchInput}
              placeholder="Search for a city (e.g., Toronto, London, Tokyo...)"
              disabled={isLoading}
            />
            <div className={styles.searchIcon}>🔍</div>
          </div>

          {showSuggestions && suggestions.length > 0 && (
            <div className={styles.suggestionsContainer}>
              <div className={styles.suggestionsHeader}>
                Select a city:
              </div>
              <ul className={styles.suggestionsList}>
                {suggestions.map((city, index) => (
                  <li
                    key={`${city.name}-${city.country}`}
                    className={`${styles.suggestionItem} ${
                      index === selectedIndex ? styles.selected : ''
                    }`}
                    onClick={() => handleCityClick(city)}
                    onMouseEnter={() => setSelectedIndex(index)}
                  >
                    <span className={styles.cityName}>{city.name}</span>
                    <span className={styles.countryName}>{city.country}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {searchTerm.length >= 2 && suggestions.length === 0 && (
            <div className={styles.noResults}>
              <p>No cities found matching "{searchTerm}"</p>
              <p>Try searching for popular cities like Toronto, London, or Tokyo</p>
            </div>
          )}

          <div className={styles.buttonGroup}>
            <button
              type="button"
              onClick={onCancel}
              className={styles.cancelButton}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={styles.searchButton}
              disabled={isLoading || suggestions.length === 0}
            >
              {isLoading ? 'Adding...' : 'Add Selected City'}
            </button>
          </div>
        </form>

        <div className={styles.quickSelect}>
          <h4 className={styles.quickSelectTitle}>Popular Cities:</h4>
          <div className={styles.popularCities}>
            {popularCities.slice(0, 8).map((city) => (
              <button
                key={`${city.name}-${city.country}`}
                className={styles.popularCityButton}
                onClick={() => handleCityClick(city)}
                disabled={isLoading}
              >
                {city.name}
              </button>
            ))}
          </div>
        </div>

        <div className={styles.searchTips}>
          <h4 className={styles.tipsTitle}>💡 Search Tips:</h4>
          <ul className={styles.tipsList}>
            <li>Type at least 2 characters to see suggestions</li>
            <li>Use arrow keys to navigate suggestions</li>
            <li>Press Enter to select highlighted city</li>
            <li>Click on popular cities for quick selection</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
