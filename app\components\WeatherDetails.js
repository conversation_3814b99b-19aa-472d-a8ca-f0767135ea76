'use client';

import styles from './WeatherDetails.module.css';

export default function WeatherDetails({ city }) {
  if (!city) {
    return (
      <div className={styles.container}>
        <div className={styles.placeholder}>
          <h3>No city selected</h3>
          <p>Please select a city from the list to view weather details.</p>
        </div>
      </div>
    );
  }

  const getWeatherIcon = (condition) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return '☀️';
      case 'cloudy':
        return '☁️';
      case 'partly cloudy':
        return '⛅';
      case 'rainy':
        return '🌧️';
      case 'snowy':
        return '❄️';
      case 'stormy':
        return '⛈️';
      default:
        return '🌤️';
    }
  };

  const getTemperatureColor = (temp) => {
    if (temp >= 30) return '#ff4444';
    if (temp >= 20) return '#ff8800';
    if (temp >= 10) return '#ffaa00';
    if (temp >= 0) return '#4488ff';
    return '#0066cc';
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.cityName}>{city.name}</h2>
        <p className={styles.country}>{city.country}</p>
      </div>

      <div className={styles.mainWeather}>
        <div className={styles.iconSection}>
          <span className={styles.weatherIcon}>
            {getWeatherIcon(city.condition)}
          </span>
        </div>
        <div className={styles.tempSection}>
          <span 
            className={styles.temperature}
            style={{ color: getTemperatureColor(city.temperature) }}
          >
            {city.temperature}°C
          </span>
          <p className={styles.condition}>{city.condition}</p>
        </div>
      </div>

      <div className={styles.description}>
        <p>{city.description}</p>
      </div>

      <div className={styles.details}>
        <div className={styles.detailItem}>
          <span className={styles.detailIcon}>💧</span>
          <div className={styles.detailInfo}>
            <span className={styles.detailLabel}>Humidity</span>
            <span className={styles.detailValue}>{city.humidity}%</span>
          </div>
        </div>

        <div className={styles.detailItem}>
          <span className={styles.detailIcon}>💨</span>
          <div className={styles.detailInfo}>
            <span className={styles.detailLabel}>Wind Speed</span>
            <span className={styles.detailValue}>{city.windSpeed} km/h</span>
          </div>
        </div>

        <div className={styles.detailItem}>
          <span className={styles.detailIcon}>🌡️</span>
          <div className={styles.detailInfo}>
            <span className={styles.detailLabel}>Feels Like</span>
            <span className={styles.detailValue}>
              {Math.round(city.temperature + (city.humidity > 70 ? 2 : -1))}°C
            </span>
          </div>
        </div>
      </div>

      <div className={styles.footer}>
        <p className={styles.lastUpdated}>
          Last updated: {new Date().toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
}
