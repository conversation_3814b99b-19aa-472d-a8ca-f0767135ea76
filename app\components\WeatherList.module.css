/* Weather List Component Styles */
.container {
  height: 100%;
}

.title {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #74b9ff;
  padding-bottom: 10px;
}

.emptyMessage {
  text-align: center;
  color: #666;
  padding: 40px 20px;
  font-style: italic;
}

.cityList {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 5px;
}

.cityList::-webkit-scrollbar {
  width: 6px;
}

.cityList::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cityList::-webkit-scrollbar-thumb {
  background: #74b9ff;
  border-radius: 3px;
}

.cityList::-webkit-scrollbar-thumb:hover {
  background: #0984e3;
}

.cityCard {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cityCard:hover {
  border-color: #74b9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}

.cityCard.selected {
  border-color: #0984e3;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
}

.cityInfo {
  flex: 1;
}

.cityName {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.country {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  opacity: 0.8;
  color: #666;
}

.weatherSummary {
  display: flex;
  gap: 15px;
  align-items: center;
}

.temperature {
  font-size: 1.1rem;
  font-weight: 700;
  color: #0984e3;
}

.cityCard.selected .temperature {
  color: white;
}

.condition {
  font-size: 0.9rem;
  padding: 4px 8px;
  background: rgba(116, 185, 255, 0.1);
  border-radius: 15px;
  font-weight: 500;
}

.cityCard.selected .condition {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.cityCard.selected .cityName {
  color: white;
}

.cityCard.selected .country {
  color: white;
}

.actions {
  display: flex;
  align-items: center;
}

.removeButton {
  background: #ff6b6b;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.removeButton:hover {
  background: #ff5252;
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .cityCard {
    padding: 12px;
  }
  
  .cityName {
    font-size: 1.1rem;
  }
  
  .weatherSummary {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .temperature {
    font-size: 1rem;
  }
  
  .condition {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.3rem;
  }
  
  .cityCard {
    padding: 10px;
  }
  
  .cityName {
    font-size: 1rem;
  }
  
  .removeButton {
    width: 25px;
    height: 25px;
    font-size: 0.9rem;
  }
}
