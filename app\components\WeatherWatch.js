'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';
import WeatherList from './WeatherList';
import WeatherDetails from './WeatherDetails';
import CitySearch from './CitySearch';
import ClientOnly from './ClientOnly';

export default function WeatherWatch() {
  const [mounted, setMounted] = useState(false);
  const [cities, setCities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState('');
  
  // Weather API configuration (using OpenWeatherMap free tier)
  const API_KEY = 'YOUR_API_KEY_HERE'; // Replace with your API key from openweathermap.org
  const API_BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';
  const GEOCODING_URL = 'https://api.openweathermap.org/geo/1.0/direct';
  
  // Default cities with coordinates for better accuracy
  const defaultCities = [
    { name: 'Toronto', country: 'CA', lat: 43.6532, lon: -79.3832 },
    { name: 'Vancouver', country: 'CA', lat: 49.2827, lon: -123.1207 },
    { name: 'Montreal', country: 'CA', lat: 45.5017, lon: -73.5673 },
    { name: 'New York', country: 'US', lat: 40.7128, lon: -74.0060 },
    { name: 'London', country: 'GB', lat: 51.5074, lon: -0.1278 }
  ];

  const [selectedCity, setSelectedCity] = useState(null);
  const [showSearch, setShowSearch] = useState(false);
  const [error, setError] = useState('');

  // Get weather background class based on condition
  const getWeatherBackgroundClass = (condition) => {
    if (!condition) return 'weather-default';
    
    const conditionLower = condition.toLowerCase();
    if (conditionLower.includes('sun') || conditionLower.includes('clear')) {
      return 'weather-sunny';
    } else if (conditionLower.includes('cloud')) {
      return conditionLower.includes('partly') ? 'weather-partly-cloudy' : 'weather-cloudy';
    } else if (conditionLower.includes('rain') || conditionLower.includes('drizzle')) {
      return 'weather-rainy';
    } else if (conditionLower.includes('snow')) {
      return 'weather-snowy';
    } else if (conditionLower.includes('storm') || conditionLower.includes('thunder')) {
      return 'weather-stormy';
    }
    return 'weather-default';
  };

  // Get coordinates for a city using geocoding API
  const getCityCoordinates = async (cityName, countryCode) => {
    try {
      if (API_KEY === 'YOUR_API_KEY_HERE') {
        // Return mock coordinates if no API key
        const mockCoords = {
          'Toronto': { lat: 43.6532, lon: -79.3832 },
          'Vancouver': { lat: 49.2827, lon: -123.1207 },
          'Montreal': { lat: 45.5017, lon: -73.5673 },
          'New York': { lat: 40.7128, lon: -74.0060 },
          'London': { lat: 51.5074, lon: -0.1278 }
        };
        return mockCoords[cityName] || { lat: 0, lon: 0 };
      }

      const response = await axios.get(
        `${GEOCODING_URL}?q=${cityName},${countryCode}&limit=1&appid=${API_KEY}`
      );
      
      if (response.data && response.data.length > 0) {
        const location = response.data[0];
        return {
          lat: location.lat,
          lon: location.lon
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting coordinates:', error);
      return null;
    }
  };

  // Fetch weather data using coordinates
  const fetchWeatherData = async (cityName, countryCode, lat = null, lon = null) => {
    try {
      let coordinates = { lat, lon };
      
      // If coordinates not provided, get them from geocoding
      if (!lat || !lon) {
        coordinates = await getCityCoordinates(cityName, countryCode);
        if (!coordinates) {
          throw new Error('Could not get coordinates for city');
        }
      }

      if (API_KEY === 'YOUR_API_KEY_HERE') {
        // Mock data for demonstration when no API key
        // Use deterministic values based on city name to prevent hydration issues
        const cityHash = cityName.split('').reduce((a, b) => {
          a = ((a << 5) - a) + b.charCodeAt(0);
          return a & a;
        }, 0);
        
        const mockConditions = ['Clear', 'Clouds', 'Rain', 'Snow', 'Thunderstorm', 'Drizzle', 'Mist'];
        const conditionIndex = Math.abs(cityHash) % mockConditions.length;
        
        return {
          id: Math.abs(cityHash),
          name: cityName,
          country: countryCode,
          temperature: Math.abs(cityHash % 30) + 5,
          condition: mockConditions[conditionIndex],
          humidity: Math.abs(cityHash % 40) + 40,
          windSpeed: Math.abs(cityHash % 20) + 5,
          description: `Current weather in ${cityName}`,
          lastUpdated: mounted ? new Date().toLocaleString() : 'Loading...',
          coordinates: coordinates
        };
      }

      // Real API call using coordinates
      const response = await axios.get(
        `${API_BASE_URL}?lat=${coordinates.lat}&lon=${coordinates.lon}&appid=${API_KEY}&units=metric`
      );
      
      const data = response.data;
      return {
        id: data.id,
        name: data.name,
        country: data.sys.country,
        temperature: Math.round(data.main.temp),
        condition: data.weather[0].main,
        humidity: data.main.humidity,
        windSpeed: Math.round(data.wind.speed * 3.6), // Convert m/s to km/h
        description: data.weather[0].description,
        lastUpdated: new Date().toLocaleString(),
        coordinates: coordinates
      };
    } catch (error) {
      console.error('Error fetching weather data:', error);
      return null;
    }
  };

  // Load initial cities
  useEffect(() => {
    const loadInitialCities = async () => {
      setLoading(true);
      const cityPromises = defaultCities.map(city => 
        fetchWeatherData(city.name, city.country, city.lat, city.lon)
      );
      
      const cityData = await Promise.all(cityPromises);
      const validCities = cityData.filter(city => city !== null);
      
      setCities(validCities);
      setLoading(false);
    };

    loadInitialCities();
  }, []);

  // Update current time (client-side only)
  useEffect(() => {
    if (mounted) {
      const updateTime = () => {
        setCurrentTime(new Date().toLocaleString());
      };
      
      updateTime();
      const timeInterval = setInterval(updateTime, 1000);
      
      return () => clearInterval(timeInterval);
    }
  }, [mounted]);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle city selection from list
  const handleCityClick = (city) => {
    setSelectedCity(city);
    setError('');
  };

  // Handle city selection from search
  const handleSearchCitySelect = async (cityData) => {
    // Check if city already exists
    const cityExists = cities.some(city => 
      city.name.toLowerCase() === cityData.name.toLowerCase()
    );

    if (cityExists) {
      setError('City already exists in the list!');
      setTimeout(() => setError(''), 3000); // Clear error after 3 seconds
      return;
    }

    setLoading(true);
    
    // Fetch real weather data for the selected city
    const weatherData = await fetchWeatherData(cityData.name, cityData.country);
    
    if (weatherData) {
      setCities([...cities, weatherData]);
      setShowSearch(false);
      setError('');
    } else {
      setError('Could not fetch weather data for this city. Please try again.');
      setTimeout(() => setError(''), 5000); // Clear error after 5 seconds
    }
    
    setLoading(false);
  };

  // Handle removing a city
  const handleRemoveCity = (cityId) => {
    setCities(cities.filter(city => city.id !== cityId));
    if (selectedCity && selectedCity.id === cityId) {
      setSelectedCity(null);
    }
  };

  // Get background class based on selected city or first city
  const backgroundWeather = selectedCity ? selectedCity.condition : (cities.length > 0 ? cities[0].condition : 'default');
  const backgroundClass = getWeatherBackgroundClass(backgroundWeather);

  const containerStyle = {
    minHeight: '100vh',
    background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
    padding: '20px',
    fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
    transition: 'background 0.5s ease',
    color: '#ffffff'
  };

  return (
    <div style={containerStyle} className={backgroundClass}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <header style={{ textAlign: 'center', marginBottom: '40px', color: 'white' }}>
          <h1 style={{ fontSize: '3rem', margin: '0 0 10px 0', fontWeight: '700', textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>
            🌤️ Weather Watch
          </h1>
          <p style={{ fontSize: '1.2rem', margin: '10px 0 0 0', opacity: '0.9', fontWeight: '300', color: '#ffffff' }}>
            Your personal weather dashboard
          </p>
          <ClientOnly fallback={<p style={{ fontSize: '1rem', margin: '10px 0 0 0', opacity: '0.8', fontWeight: '400', color: '#ffffff' }}>Current Time: Loading...</p>}>
            <p style={{ fontSize: '1rem', margin: '10px 0 0 0', opacity: '0.8', fontWeight: '400', color: '#ffffff', background: 'rgba(255, 255, 255, 0.1)', padding: '8px 16px', borderRadius: '20px', display: 'inline-block', backdropFilter: 'blur(10px)' }}>
              Current Time: {currentTime}
            </p>
          </ClientOnly>
        </header>

        <main>
          <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '20px' }}>
            <button 
              onClick={() => setShowSearch(!showSearch)}
              disabled={loading}
              style={{
                background: '#00b894',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '25px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 15px rgba(0, 184, 148, 0.3)',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              <span>{showSearch ? 'Cancel Search' : '🔍 Search Cities'}</span>
            </button>
          </div>

          {error && (
            <div style={{
              background: '#ff6b6b',
              color: 'white',
              padding: '12px 20px',
              borderRadius: '8px',
              margin: '10px auto',
              maxWidth: '500px',
              textAlign: 'center',
              fontWeight: '500',
              boxShadow: '0 4px 15px rgba(255, 107, 107, 0.3)'
            }}>
              {error}
            </div>
          )}

          {showSearch && (
            <CitySearch 
              onCitySelect={handleSearchCitySelect}
              onCancel={() => setShowSearch(false)}
              isLoading={loading}
            />
          )}

          <ClientOnly fallback={
            <div style={{
              textAlign: 'center',
              color: 'white',
              padding: '60px 20px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '15px',
              marginTop: '30px',
              backdropFilter: 'blur(10px)'
            }}>
              <h3>Loading weather data...</h3>
              <p>Please wait while we fetch the latest weather information.</p>
            </div>
          }>
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '30px',
              marginTop: '20px'
            }}>
              <div style={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '15px',
                padding: '20px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(10px)'
              }}>
                <WeatherList 
                  cities={cities}
                  onCitySelect={handleCityClick}
                  onRemoveCity={handleRemoveCity}
                  selectedCity={selectedCity}
                />
              </div>

              <div style={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '15px',
                padding: '20px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(10px)'
              }}>
                {selectedCity ? (
                  <WeatherDetails city={selectedCity} />
                ) : (
                  <div style={{ textAlign: 'center', color: '#666', padding: '40px 20px' }}>
                    <h3 style={{ margin: '0 0 10px 0', color: '#333', fontSize: '1.5rem' }}>Select a city to view weather details</h3>
                    <p style={{ margin: 0, fontSize: '1rem', opacity: '0.8', color: '#666' }}>Click on any city from the list to see detailed weather information.</p>
                  </div>
                )}
              </div>
            </div>
          </ClientOnly>

          {loading && (
            <div style={{
              textAlign: 'center',
              color: 'white',
              padding: '60px 20px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '15px',
              marginTop: '30px',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ margin: '0 0 10px 0', fontSize: '1.8rem', color: '#ffffff' }}>Loading weather data...</h3>
              <p style={{ margin: 0, fontSize: '1.1rem', opacity: '0.9', color: '#ffffff' }}>Please wait while we fetch the latest weather information.</p>
            </div>
          )}

          {!loading && cities.length === 0 && (
            <div style={{
              textAlign: 'center',
              color: 'white',
              padding: '60px 20px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '15px',
              marginTop: '30px',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 style={{ margin: '0 0 10px 0', fontSize: '1.8rem' }}>No cities added yet</h3>
              <p style={{ margin: 0, fontSize: '1.1rem', opacity: '0.9' }}>Add your first city to start tracking weather!</p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
