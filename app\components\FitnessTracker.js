'use client';

import { useState, useEffect } from 'react';
import FitnessDashboard from './fitness/Dashboard';
import WorkoutTracker from './fitness/WorkoutTracker';
import ClientOnly from './ClientOnly';

export default function FitnessTracker() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [workouts, setWorkouts] = useState([]);
  const [goals, setGoals] = useState([]);
  const [progress, setProgress] = useState([]);
  const [mounted, setMounted] = useState(false);

  // Sample data for demonstration
  useEffect(() => {
    if (mounted) {
      // Initialize with sample data
      setWorkouts([
        {
          id: 1,
          name: 'Morning Run',
          type: 'Cardio',
          duration: 30,
          calories: 300,
          date: new Date().toISOString().split('T')[0],
          notes: 'Great morning run in the park'
        },
        {
          id: 2,
          name: 'Strength Training',
          type: 'Strength',
          duration: 45,
          calories: 250,
          date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
          notes: 'Upper body workout'
        }
      ]);

      setGoals([
        {
          id: 1,
          title: 'Run 5K',
          target: 5000,
          current: 3200,
          unit: 'meters',
          deadline: '2024-12-31',
          category: 'Cardio'
        },
        {
          id: 2,
          title: 'Lose Weight',
          target: 10,
          current: 6,
          unit: 'kg',
          deadline: '2024-12-31',
          category: 'Weight Loss'
        }
      ]);

      setProgress([
        {
          id: 1,
          date: new Date().toISOString().split('T')[0],
          weight: 75,
          bodyFat: 15,
          muscle: 40,
          notes: 'Feeling strong today'
        }
      ]);
    }
  }, [mounted]);

  useEffect(() => {
    setMounted(true);
  }, []);

  const addWorkout = (workout) => {
    const newWorkout = {
      ...workout,
      id: Date.now(),
      date: new Date().toISOString().split('T')[0]
    };
    setWorkouts([newWorkout, ...workouts]);
  };

  const deleteWorkout = (id) => {
    setWorkouts(workouts.filter(workout => workout.id !== id));
  };

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'workouts', name: 'Workouts', icon: '💪' }
  ];

  const tabButtonStyle = {
    padding: '12px 24px',
    border: 'none',
    borderRadius: '25px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    fontSize: '14px',
    backdropFilter: 'blur(10px)'
  };

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', padding: '20px 0' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
        {/* Header */}
        <header style={{ textAlign: 'center', marginBottom: '40px', color: 'white' }}>
          <h1 style={{ fontSize: '3rem', margin: '0 0 10px 0', fontWeight: '700', textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>
            💪 Fitness Tracker
          </h1>
          <p style={{ fontSize: '1.2rem', opacity: '0.9', fontWeight: '300' }}>
            Your personal health and fitness dashboard
          </p>
        </header>

        {/* Navigation Tabs */}
        <nav style={{ marginBottom: '30px' }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            gap: '10px',
            flexWrap: 'wrap'
          }}>
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  ...tabButtonStyle,
                  background: activeTab === tab.id 
                    ? 'rgba(255, 255, 255, 0.9)' 
                    : 'rgba(255, 255, 255, 0.2)',
                  color: activeTab === tab.id ? '#333' : 'white',
                  transform: activeTab === tab.id ? 'translateY(-2px)' : 'none',
                  boxShadow: activeTab === tab.id 
                    ? '0 4px 15px rgba(0, 0, 0, 0.2)' 
                    : 'none'
                }}
              >
                <span style={{ fontSize: '16px' }}>{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </div>
        </nav>

        {/* Main Content */}
        <ClientOnly fallback={
          <div style={{ 
            background: 'white',
            borderRadius: '12px',
            padding: '60px 20px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e5e7eb',
            textAlign: 'center'
          }}>
            <h3>Loading Fitness Tracker...</h3>
            <p>Please wait while we prepare your fitness dashboard.</p>
          </div>
        }>
          <main style={{ 
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: '15px',
            padding: '30px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(10px)'
          }}>
            {activeTab === 'dashboard' && (
              <FitnessDashboard 
                workouts={workouts}
                goals={goals}
                progress={progress}
              />
            )}
            
            {activeTab === 'workouts' && (
              <WorkoutTracker 
                workouts={workouts}
                onAddWorkout={addWorkout}
                onDeleteWorkout={deleteWorkout}
              />
            )}
          </main>
        </ClientOnly>
      </div>
    </div>
  );
}
