/* Add City Form Component Styles */
.container {
  margin-bottom: 20px;
}

.formCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(116, 185, 255, 0.3);
  max-width: 500px;
  margin: 0 auto;
}

.title {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  border-bottom: 2px solid #74b9ff;
  padding-bottom: 10px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.input,
.select,
.textarea {
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  background: white;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #74b9ff;
  box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
}

.input::placeholder,
.textarea::placeholder {
  color: #999;
  font-style: italic;
}

.inputError {
  border-color: #ff6b6b;
}

.inputError:focus {
  border-color: #ff6b6b;
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.select {
  cursor: pointer;
}

.textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.errorMessage {
  color: #ff6b6b;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.errorMessage::before {
  content: "⚠️";
  font-size: 0.8rem;
}

.buttonGroup {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 10px;
}

.submitButton,
.cancelButton {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.submitButton {
  background: #00b894;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.submitButton:hover {
  background: #00a085;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
}

.submitButton:active {
  transform: translateY(0);
}

.cancelButton {
  background: #6c757d;
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.cancelButton:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.cancelButton:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .formCard {
    padding: 20px;
    margin: 0 10px;
  }
  
  .title {
    font-size: 1.3rem;
  }
  
  .buttonGroup {
    flex-direction: column;
    gap: 10px;
  }
  
  .submitButton,
  .cancelButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .formCard {
    padding: 15px;
    margin: 0 5px;
  }
  
  .title {
    font-size: 1.2rem;
  }
  
  .input,
  .select,
  .textarea {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
  
  .submitButton,
  .cancelButton {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .label {
    font-size: 0.8rem;
  }
  
  .errorMessage {
    font-size: 0.8rem;
  }
}

/* Animation for form appearance */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.formCard {
  animation: slideIn 0.3s ease-out;
}
