/* Weather Details Component Styles */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  color: #666;
  padding: 40px 20px;
}

.placeholder h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.5rem;
}

.placeholder p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.8;
  color: #666;
}

.header {
  text-align: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #74b9ff;
}

.cityName {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
}

.country {
  margin: 0;
  color: #666;
  font-size: 1rem;
  font-weight: 400;
}

.mainWeather {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 15px;
  color: white;
}

.iconSection {
  display: flex;
  align-items: center;
  justify-content: center;
}

.weatherIcon {
  font-size: 4rem;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.tempSection {
  text-align: center;
}

.temperature {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.condition {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.9;
}

.description {
  text-align: center;
  margin-bottom: 25px;
  padding: 15px;
  background: rgba(116, 185, 255, 0.1);
  border-radius: 10px;
  border-left: 4px solid #74b9ff;
}

.description p {
  margin: 0;
  font-size: 1rem;
  color: #333;
  font-style: italic;
}

.details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.detailItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.detailItem:hover {
  border-color: #74b9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(116, 185, 255, 0.2);
}

.detailIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.detailInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detailLabel {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailValue {
  font-size: 1.1rem;
  color: #333;
  font-weight: 700;
}

.footer {
  margin-top: auto;
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.lastUpdated {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mainWeather {
    flex-direction: column;
    gap: 20px;
    padding: 15px;
  }
  
  .weatherIcon {
    font-size: 3rem;
  }
  
  .temperature {
    font-size: 2.5rem;
  }
  
  .condition {
    font-size: 1.1rem;
  }
  
  .cityName {
    font-size: 1.5rem;
  }
  
  .details {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .detailItem {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .weatherIcon {
    font-size: 2.5rem;
  }
  
  .temperature {
    font-size: 2rem;
  }
  
  .condition {
    font-size: 1rem;
  }
  
  .cityName {
    font-size: 1.3rem;
  }
  
  .detailIcon {
    font-size: 1.2rem;
  }
  
  .detailValue {
    font-size: 1rem;
  }
}
