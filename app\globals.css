/* Weather Watch Global Styles */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #ffffff;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

* {
  box-sizing: border-box;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Ensure text is always visible */
h1, h2, h3, h4, h5, h6 {
  color: inherit;
  font-weight: inherit;
}

p, span, div {
  color: inherit;
}

/* Weather condition backgrounds */
.weather-sunny {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.weather-cloudy {
  background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
}

.weather-rainy {
  background: linear-gradient(135deg, #4682B4 0%, #2F4F4F 100%);
}

.weather-snowy {
  background: linear-gradient(135deg, #E6E6FA 0%, #B0C4DE 100%);
}

.weather-stormy {
  background: linear-gradient(135deg, #2F4F4F 0%, #000000 100%);
}

.weather-partly-cloudy {
  background: linear-gradient(135deg, #87CEEB 0%, #FFD700 100%);
}

.weather-default {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}
