/* Main Navigation Styles */
.navigation {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.brand {
  color: white;
  text-align: left;
}

.brandTitle {
  font-size: 2rem;
  margin: 0 0 5px 0;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.brandSubtitle {
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

.appSelector {
  display: flex;
  gap: 15px;
  align-items: center;
}

.appButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border: none;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  min-width: 200px;
  text-align: left;
}

.appButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.appButton.active {
  background: var(--app-color);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.appIcon {
  font-size: 1.8rem;
  flex-shrink: 0;
}

.appInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.appName {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
}

.appDescription {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .brand {
    text-align: center;
  }
  
  .brandTitle {
    font-size: 1.8rem;
  }
  
  .appSelector {
    flex-direction: column;
    width: 100%;
    gap: 10px;
  }
  
  .appButton {
    width: 100%;
    justify-content: center;
    min-width: auto;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .navigation {
    padding: 15px 0;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .brandTitle {
    font-size: 1.5rem;
  }
  
  .brandSubtitle {
    font-size: 0.8rem;
  }
  
  .appButton {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
  }
  
  .appIcon {
    font-size: 1.5rem;
  }
  
  .appName {
    font-size: 0.9rem;
  }
  
  .appDescription {
    font-size: 0.7rem;
  }
}

/* Animation for smooth transitions */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.navigation {
  animation: slideIn 0.5s ease-out;
}
