'use client';

import { useState } from 'react';
import styles from './MainNavigation.module.css';

export default function MainNavigation({ activeApp, onAppChange }) {
  const apps = [
    { 
      id: 'weather', 
      name: 'Weather Watch', 
      icon: '🌤️', 
      description: 'Track weather conditions worldwide',
      color: '#74b9ff'
    },
    { 
      id: 'fitness', 
      name: 'Fitness Tracker', 
      icon: '💪', 
      description: 'Monitor your health and fitness goals',
      color: '#667eea'
    }
  ];

  return (
    <nav className={styles.navigation}>
      <div className={styles.container}>
        <div className={styles.brand}>
          <h1 className={styles.brandTitle}>🌟 Life Dashboard</h1>
          <p className={styles.brandSubtitle}>Your personal health & weather companion</p>
        </div>
        
        <div className={styles.appSelector}>
          {apps.map(app => (
            <button
              key={app.id}
              onClick={() => onAppChange(app.id)}
              className={`${styles.appButton} ${activeApp === app.id ? styles.active : ''}`}
              style={{
                '--app-color': app.color,
                background: activeApp === app.id 
                  ? `linear-gradient(135deg, ${app.color}, ${app.color}dd)` 
                  : 'rgba(255, 255, 255, 0.1)'
              }}
            >
              <span className={styles.appIcon}>{app.icon}</span>
              <div className={styles.appInfo}>
                <span className={styles.appName}>{app.name}</span>
                <span className={styles.appDescription}>{app.description}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
}
